import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'master_slave_extension.dart';

/// 闸机配置服务
/// 管理主从机配置和初始化
class GateConfigService {
  static final _instance = GateConfigService._internal();
  static GateConfigService get instance => _instance;
  GateConfigService._internal();

  // 配置键
  static const String _configKey = 'master_slave_config';

  // 当前配置
  MasterSlaveConfig? _currentConfig;

  // 扩展管理器
  MasterSlaveExtension? _extension;

  /// 获取当前配置
  MasterSlaveConfig? get currentConfig => _currentConfig;

  /// 获取扩展管理器
  MasterSlaveExtension? get extension => _extension;

  /// 初始化主从机扩展
  Future<void> initialize() async {
    try {
      debugPrint('开始初始化主从机扩展...');

      // 加载配置
      await _loadConfig();

      if (_currentConfig == null) {
        debugPrint('未找到配置，主从机扩展未启用');
        return;
      }

      // 启用扩展
      _extension = MasterSlaveExtension.instance;
      await _extension!.enable(
        channelId: _currentConfig!.channelId,
        isMaster: _currentConfig!.isMaster,
        slaveAddress: _currentConfig!.slaveAddress,
        masterAddress: _currentConfig!.masterAddress,
        port: _currentConfig!.port,
      );

      debugPrint('主从机扩展初始化完成');
      debugPrint('配置信息: $_currentConfig');

    } catch (e) {
      debugPrint('初始化主从机扩展失败: $e');
      rethrow;
    }
  }

  /// 🔥 优化：配置为主机模式（不需要从机地址）
  Future<void> configureAsMaster({
    required String channelId,
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为主机模式: $channelId，监听端口: $port');

      _currentConfig = MasterSlaveConfig(
        channelId: channelId,
        isMaster: true,
        slaveAddress: null, // 🔥 不再需要从机地址
        port: port,
      );

      await _saveConfig();
      await _reinitialize();

    } catch (e) {
      debugPrint('配置主机模式失败: $e');
      rethrow;
    }
  }

  /// 配置为从机模式
  Future<void> configureAsSlave({
    required String channelId,
    required String masterAddress,
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为从机模式: $channelId');

      _currentConfig = MasterSlaveConfig(
        channelId: channelId,
        isMaster: false,
        masterAddress: masterAddress,
        port: port,
      );

      await _saveConfig();
      await _reinitialize();

    } catch (e) {
      debugPrint('配置从机模式失败: $e');
      rethrow;
    }
  }

  /// 重新初始化系统
  Future<void> _reinitialize() async {
    // 清理旧的扩展
    _extension?.disable();
    _extension = null;

    // 重新初始化
    await initialize();
  }

  /// 加载配置
  Future<void> _loadConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configStr = prefs.getString(_configKey);
      
      if (configStr != null) {
        final configMap = jsonDecode(configStr);
        _currentConfig = MasterSlaveConfig.fromJson(configMap);
        debugPrint('加载配置成功: ${_currentConfig!.channelId}');
      }
    } catch (e) {
      debugPrint('加载配置失败: $e');
    }
  }

  /// 保存配置
  Future<void> _saveConfig() async {
    if (_currentConfig == null) return;
    
    try {
      final prefs = await SharedPreferences.getInstance();
      final configStr = jsonEncode(_currentConfig!.toJson());
      await prefs.setString(_configKey, configStr);
      debugPrint('保存配置成功');
    } catch (e) {
      debugPrint('保存配置失败: $e');
    }
  }

  /// 获取系统状态
  Map<String, dynamic> getSystemStatus() {
    return {
      'config': _currentConfig?.toJson(),
      'extension_status': _extension?.getStatus(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 清理资源
  void dispose() {
    _extension?.dispose();
    _extension = null;
  }
}
