import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'gate_config_service.dart';
import 'master_slave_extension.dart';

/// 从机专用设置
/// 用于第二台设备配置为从机模式
class SlaveSetup {
  
  /// 快速启用从机模式
  static Future<void> enableSlave({
    String masterAddress = '************', // 主机IP
    int port = 8888,
  }) async {
    try {
      debugPrint('🚀 快速启用从机模式...');
      debugPrint('📡 主机地址: $masterAddress:$port');
      
      await GateConfigService.instance.configureAsSlave(
        channelId: 'channel_2',
        masterAddress: masterAddress,
        port: port,
      );
      
      await GateConfigService.instance.initialize();
      
      debugPrint('✅ 从机模式启用成功！');
      debugPrint('📊 状态: ${MasterSlaveExtension.instance.getStatus()}');
      
    } catch (e) {
      debugPrint('❌ 启用从机失败: $e');
      rethrow;
    }
  }
  
  /// 禁用扩展
  static void disable() {
    debugPrint('🛑 禁用从机扩展...');
    MasterSlaveExtension.instance.disable();
    debugPrint('✅ 从机扩展已禁用');
  }
  
  /// 检查从机状态
  static void checkStatus() {
    final extension = MasterSlaveExtension.instance;
    final status = extension.getStatus();
    
    debugPrint('📊 从机当前状态:');
    debugPrint('  启用: ${status['enabled']}');
    debugPrint('  通道: ${status['channel_id']}');
    debugPrint('  主机: ${status['is_master']}');
    debugPrint('  共享池: ${status['shared_pool_size']}个条码');
    debugPrint('  队列: ${status['queue_size']}个条码');
    debugPrint('  连接: ${status['comm_connected']}');
  }
  
  /// 查看从机收集的条码
  static void showCollectedBarcodes() {
    final extension = MasterSlaveExtension.instance;
    
    if (!extension.isEnabled) {
      debugPrint('❌ 从机扩展未启用');
      return;
    }
    
    final sharedPool = extension.getSharedPoolBarcodes();
    final collected = extension.getCollectedBarcodes();
    
    debugPrint('📊 从机数据状态:');
    debugPrint('  共享池条码: $sharedPool');
    debugPrint('  收集的条码: $collected');
  }
  
  /// 模拟从机出馆流程
  static Future<void> simulateSlaveExitFlow() async {
    final extension = MasterSlaveExtension.instance;
    
    if (!extension.isEnabled || extension.isMaster) {
      debugPrint('❌ 请先启用从机模式');
      return;
    }
    
    debugPrint('🎭 模拟从机出馆流程...');
    
    // 1. 模拟出馆开始
    debugPrint('🚪 从机：出馆开始');
    extension.triggerExitStart();
    
    // 2. 等待数据同步
    debugPrint('⏳ 等待主机数据同步...');
    await Future.delayed(const Duration(seconds: 3));
    
    // 3. 显示同步的数据
    showCollectedBarcodes();
    
    // 4. 模拟到达位置
    debugPrint('📍 从机：到达位置');
    extension.triggerPositionReached();
    
    // 5. 等待收集结果
    await Future.delayed(const Duration(seconds: 2));
    
    // 6. 显示最终结果
    final collectedBarcodes = extension.getCollectedBarcodes();
    debugPrint('📊 从机最终收集结果: $collectedBarcodes');
    
    debugPrint('✅ 从机出馆流程模拟完成');
  }
  
  /// 测试网络连接
  static Future<void> testConnection({
    String masterAddress = '************',
    int port = 8888,
  }) async {
    debugPrint('🔍 测试与主机的网络连接...');
    debugPrint('📡 目标: $masterAddress:$port');
    
    try {
      final socket = await Socket.connect(masterAddress, port);
      debugPrint('✅ 网络连接成功');
      await socket.close();
    } catch (e) {
      debugPrint('❌ 网络连接失败: $e');
      debugPrint('💡 请检查:');
      debugPrint('   1. 主机是否已启动');
      debugPrint('   2. IP地址是否正确');
      debugPrint('   3. 端口是否开放');
      debugPrint('   4. 防火墙设置');
      rethrow;
    }
  }
  
  /// 运行完整的从机测试
  static Future<void> runFullSlaveTest({
    String masterAddress = '************',
  }) async {
    debugPrint('🧪 开始完整从机测试...');
    
    try {
      // 1. 测试网络连接
      await testConnection(masterAddress: masterAddress);
      
      // 2. 启用从机
      await enableSlave(masterAddress: masterAddress);
      
      // 3. 等待连接建立
      await Future.delayed(const Duration(seconds: 2));
      
      // 4. 检查状态
      checkStatus();
      
      // 5. 模拟出馆流程
      await simulateSlaveExitFlow();
      
      debugPrint('✅ 完整从机测试完成');
    } catch (e) {
      debugPrint('❌ 从机测试失败: $e');
      rethrow;
    }
  }
  
  /// 监听主机数据变化
  static void startMonitoring() {
    debugPrint('👂 开始监听主机数据变化...');
    
    final extension = MasterSlaveExtension.instance;
    
    // 定时检查数据变化
    Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!extension.isEnabled) {
        timer.cancel();
        return;
      }
      
      final status = extension.getStatus();
      final poolSize = status['shared_pool_size'] ?? 0;
      final queueSize = status['queue_size'] ?? 0;
      final connected = status['comm_connected'] ?? false;
      
      debugPrint('📊 [${DateTime.now().toString().substring(11, 19)}] '
                '连接:$connected 共享池:$poolSize 队列:$queueSize');
    });
  }
}
