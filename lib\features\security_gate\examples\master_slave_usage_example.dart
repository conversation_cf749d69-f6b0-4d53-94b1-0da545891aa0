import 'dart:async';
import 'package:flutter/foundation.dart';
import '../services/gate_config_service.dart';
import '../services/master_slave_extension.dart';

/// 主从机使用示例
/// 展示如何配置和使用主从机扩展
class MasterSlaveUsageExample {
  
  /// 示例1：配置主机（通道1）
  static Future<void> setupMasterExample() async {
    try {
      debugPrint('=== 配置主机示例 ===');
      
      // 1. 🔥 优化：配置为主机模式（不需要从机IP）
      await GateConfigService.instance.configureAsMaster(
        channelId: 'channel_1',
        port: 8888,
      );
      
      // 2. 初始化系统
      await GateConfigService.instance.initialize();
      
      // 3. 获取扩展管理器
      final extension = GateConfigService.instance.extension;
      if (extension == null) {
        debugPrint('扩展管理器初始化失败');
        return;
      }
      
      debugPrint('主机配置完成');
      debugPrint('状态: ${extension.getStatus()}');
      
    } catch (e) {
      debugPrint('配置主机失败: $e');
    }
  }

  /// 示例2：配置从机（通道2）
  static Future<void> setupSlaveExample() async {
    try {
      debugPrint('=== 配置从机示例 ===');
      
      // 1. 配置为从机模式
      await GateConfigService.instance.configureAsSlave(
        channelId: 'channel_2',
        masterAddress: '************', // 主机IP地址
        port: 8888,
      );
      
      // 2. 初始化系统
      await GateConfigService.instance.initialize();
      
      // 3. 获取扩展管理器
      final extension = GateConfigService.instance.extension;
      if (extension == null) {
        debugPrint('扩展管理器初始化失败');
        return;
      }
      
      debugPrint('从机配置完成');
      debugPrint('状态: ${extension.getStatus()}');
      
    } catch (e) {
      debugPrint('配置从机失败: $e');
    }
  }

  /// 示例3：测试主机功能
  static void testMasterFunctions() {
    debugPrint('=== 测试主机功能 ===');
    
    final extension = MasterSlaveExtension.instance;
    if (!extension.isEnabled || !extension.isMaster) {
      debugPrint('主机扩展未启用');
      return;
    }
    
    // 1. 添加测试条码到共享池
    debugPrint('1. 添加测试条码到共享池');
    extension.addTestBarcode('BOOK001');
    extension.addTestBarcode('BOOK002');
    extension.addTestBarcode('BOOK003');
    
    // 2. 查看共享池状态
    debugPrint('2. 共享池条码: ${extension.getSharedPoolBarcodes()}');
    
    // 3. 触发位置到达事件
    debugPrint('3. 触发位置到达事件');
    extension.triggerPositionReached();
    
    // 4. 查看收集结果
    Future.delayed(const Duration(seconds: 2), () {
      debugPrint('4. 收集到的条码: ${extension.getCollectedBarcodes()}');
    });
  }

  /// 示例4：测试从机功能
  static void testSlaveFunctions() {
    debugPrint('=== 测试从机功能 ===');
    
    final extension = MasterSlaveExtension.instance;
    if (!extension.isEnabled || extension.isMaster) {
      debugPrint('从机扩展未启用');
      return;
    }
    
    // 1. 查看共享池状态（从主机同步）
    debugPrint('1. 共享池条码: ${extension.getSharedPoolBarcodes()}');
    
    // 2. 触发位置到达事件
    debugPrint('2. 触发位置到达事件');
    extension.triggerPositionReached();
    
    // 3. 查看收集结果
    Future.delayed(const Duration(seconds: 2), () {
      debugPrint('3. 收集到的条码: ${extension.getCollectedBarcodes()}');
    });
  }

  /// 示例5：监控系统状态
  static void monitorSystemStatus() {
    debugPrint('=== 系统状态监控 ===');
    
    // 定时打印系统状态
    Timer.periodic(const Duration(seconds: 5), (timer) {
      final configService = GateConfigService.instance;
      final systemStatus = configService.getSystemStatus();
      
      debugPrint('系统状态: $systemStatus');
      
      final extension = configService.extension;
      if (extension != null) {
        debugPrint('扩展状态: ${extension.getStatus()}');
        debugPrint('共享池大小: ${extension.getSharedPoolBarcodes().length}');
        debugPrint('队列大小: ${extension.getCollectedBarcodes().length}');
      }
      debugPrint('---');
    });
  }

  /// 示例6：完整的主机演示
  static Future<void> fullMasterDemo() async {
    try {
      debugPrint('=== 完整主机演示 ===');
      
      // 1. 配置主机
      await setupMasterExample();
      
      // 2. 等待一段时间
      await Future.delayed(const Duration(seconds: 2));
      
      // 3. 测试功能
      testMasterFunctions();
      
      // 4. 开始监控
      monitorSystemStatus();
      
      debugPrint('完整主机演示启动完成');
      
    } catch (e) {
      debugPrint('完整主机演示失败: $e');
    }
  }

  /// 示例7：完整的从机演示
  static Future<void> fullSlaveDemo() async {
    try {
      debugPrint('=== 完整从机演示 ===');
      
      // 1. 配置从机
      await setupSlaveExample();
      
      // 2. 等待一段时间
      await Future.delayed(const Duration(seconds: 2));
      
      // 3. 测试功能
      testSlaveFunctions();
      
      // 4. 开始监控
      monitorSystemStatus();
      
      debugPrint('完整从机演示启动完成');
      
    } catch (e) {
      debugPrint('完整从机演示失败: $e');
    }
  }

  /// 示例8：禁用扩展
  static void disableExtension() {
    debugPrint('=== 禁用扩展 ===');
    
    final extension = MasterSlaveExtension.instance;
    extension.disable();
    
    debugPrint('主从机扩展已禁用');
  }

  /// 示例9：获取详细状态信息
  static void getDetailedStatus() {
    debugPrint('=== 详细状态信息 ===');
    
    final configService = GateConfigService.instance;
    final config = configService.currentConfig;
    final extension = configService.extension;
    
    if (config != null) {
      debugPrint('配置信息:');
      debugPrint('  通道ID: ${config.channelId}');
      debugPrint('  是否主机: ${config.isMaster}');
      debugPrint('  从机地址: ${config.slaveAddress}');
      debugPrint('  主机地址: ${config.masterAddress}');
      debugPrint('  端口: ${config.port}');
      debugPrint('  配置有效: ${config.isValid()}');
    }
    
    if (extension != null) {
      debugPrint('扩展状态:');
      debugPrint('  已启用: ${extension.isEnabled}');
      debugPrint('  通道ID: ${extension.channelId}');
      debugPrint('  是否主机: ${extension.isMaster}');
      
      final status = extension.getStatus();
      debugPrint('  详细状态: $status');
    }
  }
}


