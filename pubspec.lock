# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: eb376e9acf6938204f90eb3b1f00b578640d3188b4c8a8ec054f9f479af8d051
      url: "https://pub.dev"
    source: hosted
    version: "64.0.0"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: "69f54f967773f6c26c7dcb13e93d7ccee8b17a641689da39e878d5cf13b06893"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  animated_reorderable_list:
    dependency: transitive
    description:
      name: animated_reorderable_list
      sha256: "5de5cca556a8c9c8f7b65234ae4b683593dc6e167db498744a5e389302f24d13"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: "7cf60b9f0cc88203c5a190b4cd62a99feea42759a7fa695010eb5de1c0b2252a"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  audioplayers:
    dependency: transitive
    description:
      name: audioplayers
      sha256: c05c6147124cd63e725e861335a8b4d57300b80e6e92cea7c145c739223bbaef
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: b00e1a0e11365d88576320ec2d8c192bc21f1afb6c0e5995d1c57ae63156acb5
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "3034e99a6df8d101da0f5082dcca0a2a99db62ab1d4ddb3277bed3f6f81afe08"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "60787e73fefc4d2e0b9c02c69885402177e818e4e27ef087074cf27c02246c9e"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "365c547f1bb9e77d94dd1687903a668d8f7ac3409e48e6e6a3668a1ac2982adb"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "22cd0173e54d92bd9b2c80b1204eb1eb159ece87475ab58c9788a70ec43c2a62"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "9536812c9103563644ada2ef45ae523806b0745f7a78e89d1b5fb1951de90e1a"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  barcode:
    dependency: transitive
    description:
      name: barcode
      sha256: "7b6729c37e3b7f34233e2318d866e8c48ddb46c1f7ad01ff7bb2a8de1da2b9f4"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.9"
  barcode_widget:
    dependency: "direct main"
    description:
      name: barcode_widget
      sha256: "6f2c5b08659b1a5f4d88d183e6007133ea2f96e50e7b8bb628f03266c3931427"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  base_package:
    dependency: "direct main"
    description:
      path: "../base_package"
      relative: true
    source: path
    version: "0.0.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "80184af8b6cb3e5c1c4ec6d8544d27711700bc3e6d2efad04238c7b5290889f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "0343061a33da9c5810b2d6cee51945127d8f4c060b7fbdd9d54917f0a3feaaa1"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "339086358431fa15d7eca8b6a36e5d783728cf025e559b834f4609a1fcfb7b0a"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: "3ac61a79bfb6f6cc11f693591063a7f19a7af628dc52f141743edac5c16e8c22"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.9"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "4ae8ffe5ac758da294ecf1802f2aff01558d8b1b00616aa7538ea9a8a5d50799"
      url: "https://pub.dev"
    source: hosted
    version: "7.3.0"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: ba95c961bafcd8686d1cf63be864eb59447e795e124d98d6a27d91fcd13602fb
      url: "https://pub.dev"
    source: hosted
    version: "8.11.1"
  card_swiper:
    dependency: "direct main"
    description:
      name: card_swiper
      sha256: "21e52a144decbf0054e7cfed8bbe46fc89635e6c86b767eaccfe7d5aeba32528"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb0f1107cac15a5ea6ef0a6ef71a807b9e4267c713bb93e00e92d737cc8dbd8a
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: f692079e25e7869c14132d39f223f8eec9830eb76131925143b2129c4bb01b37
      url: "https://pub.dev"
    source: hosted
    version: "4.10.0"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: f092b211a4319e98e5ff58223576de6c2803db36221657b46c82574721240687
      url: "https://pub.dev"
    source: hosted
    version: "1.17.2"
  color:
    dependency: transitive
    description:
      name: color
      sha256: ddcdf1b3badd7008233f5acffaf20ca9f5dc2cd0172b75f68f24526a5f5725cb
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: b74247fad72c171381dbe700ca17da24deac637ab6d43c343b42867acb95c991
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "2f9d2cbccb76127ba28528cb3ae2c2326a122446a83de5a056aaa3880d3882c5"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+7"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  dart_des:
    dependency: transitive
    description:
      name: dart_des
      sha256: "0a66afb8883368c824497fd2a1fd67bdb1a785965a3956728382c03d40747c33"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  dart_sm:
    dependency: transitive
    description:
      name: dart_sm
      sha256: "98051cca380155c21dc33aef8425d4877f44e857801fd622914bd07d9c06d3dc"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "99e066ce75c89d6b29903d788a7bb9369cf754f7b24bf70bf4b6d6d6b26853b9"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.6"
  dartx:
    dependency: transitive
    description:
      name: dartx
      sha256: "8b25435617027257d43e6508b5fe061012880ddfdaa75a71d607c3de2a13d244"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  date_format:
    dependency: transitive
    description:
      name: date_format
      sha256: a48254e60bdb7f1d5a15cac7f86e37491808056c0a99dbdc850841def4754ddc
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: d90ee57923d1828ac14e492ca49440f65477f4bb1263575900be731a3dac66a9
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "0a2e95fc6bdeb623bb623fc41e90e6924e9a3bbd65089f9221f83c185366b479"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  dotted_line:
    dependency: "direct main"
    description:
      name: dotted_line
      sha256: "41e3d655939559815daa1370fc1e07673a205fa628cf40ce3af45d90029a77b6"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.3"
  dropdown_search:
    dependency: "direct main"
    description:
      name: dropdown_search
      sha256: "55106e8290acaa97ed15bea1fdad82c3cf0c248dd410e651f5a8ac6870f783ab"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.6"
  dylib:
    dependency: transitive
    description:
      name: dylib
      sha256: bf609b3eb6492a3309b3d1dbe8f83a4031de5535dd7686be33487051cc760bb0
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "1a55e97923769c286d295240048fc180e7b0768902c3c2e869fe059aafa15304"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  fast_gbk:
    dependency: "direct main"
    description:
      name: fast_gbk
      sha256: "3f0dc0309986f615404ea8d1fccf00a535dc00ee90b88d71492866bd95586489"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  ffi:
    dependency: "direct main"
    description:
      name: ffi
      sha256: "7bf0adc28a23d395f19f3f1eb21dd7cfd1dd9f8e1c50051c069122e6853bc878"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  file_picker:
    dependency: "direct main"
    description:
      name: file_picker
      sha256: be325344c1f3070354a1d84a231a1ba75ea85d413774ec4bdf444c023342e030
      url: "https://pub.dev"
    source: hosted
    version: "5.5.0"
  file_selector:
    dependency: "direct main"
    description:
      name: file_selector
      sha256: "5019692b593455127794d5718304ff1ae15447dea286cdda9f0db2a796a1b828"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  file_selector_android:
    dependency: transitive
    description:
      name: file_selector_android
      sha256: "57265ec9591e8fd8508f613544cde6f7d045731f6b09644057e49a4c9c672b7c"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1+1"
  file_selector_ios:
    dependency: transitive
    description:
      name: file_selector_ios
      sha256: b015154e6d9fddbc4d08916794df170b44531798c8dd709a026df162d07ad81d
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1+8"
  file_selector_linux:
    dependency: transitive
    description:
      name: file_selector_linux
      sha256: "045d372bf19b02aeb69cacf8b4009555fb5f6f0b7ad8016e5f46dd1387ddd492"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_macos:
    dependency: transitive
    description:
      name: file_selector_macos
      sha256: f42eacb83b318e183b1ae24eead1373ab1334084404c8c16e0354f9a3e55d385
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  file_selector_platform_interface:
    dependency: transitive
    description:
      name: file_selector_platform_interface
      sha256: a3994c26f10378a039faa11de174d7b78eb8f79e4dd0af2a451410c1a5c3f66b
      url: "https://pub.dev"
    source: hosted
    version: "2.6.2"
  file_selector_web:
    dependency: transitive
    description:
      name: file_selector_web
      sha256: dc6622c4d66cb1bee623ddcc029036603c6cc45c85e4a775bb06008d61c809c1
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2+1"
  file_selector_windows:
    dependency: transitive
    description:
      name: file_selector_windows
      sha256: d3547240c20cabf205c7c7f01a50ecdbc413755814d6677f3cb366f04abcead0
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+1"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.dev"
    source: hosted
    version: "3.0.5"
  flutter_gen_core:
    dependency: transitive
    description:
      name: flutter_gen_core
      sha256: "53890b653738f34363d9f0d40f82104c261716bd551d3ba65f648770b6764c21"
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  flutter_gen_runner:
    dependency: "direct dev"
    description:
      name: flutter_gen_runner
      sha256: de70b42eb5329f712c8b041069d081ad5fb5109f32d6d1ea9c1b39596786215d
      url: "https://pub.dev"
    source: hosted
    version: "5.9.0"
  flutter_libserialport:
    dependency: transitive
    description:
      name: flutter_libserialport
      sha256: "20c320dcde8592a16f9badc0cacad61b1fb283dbec647b6ebfc1020f8274c67b"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  flutter_localization:
    dependency: "direct main"
    description:
      name: flutter_localization
      sha256: faaeb1eba307473032e2c2af737f36ced61fc98735608410d0a6d9c231b50912
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_multi_slider:
    dependency: "direct main"
    description:
      name: flutter_multi_slider
      sha256: "3c4603999f5c399b57b5a85086cf92c8870e726fe07fd63bc3968bd3e564518b"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "592dc01a18961a51c24ae5d963b724b2b7fa4a95c100fe8eb6ca8a5a4732cadf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.18"
  flutter_spinkit:
    dependency: "direct main"
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: f64a0333a82f30b0cca061bc3d143813a486dc086b574bfb233b7c1372427694
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.dev"
    source: hosted
    version: "4.6.6"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  go_router:
    dependency: "direct main"
    description:
      name: go_router
      sha256: "170c46e237d6eb0e6e9f0e8b3f56101e14fb64f787016e42edd74c39cf8b176a"
      url: "https://pub.dev"
    source: hosted
    version: "13.2.0"
  gradient_borders:
    dependency: "direct main"
    description:
      name: gradient_borders
      sha256: b1cd969552c83f458ff755aa68e13a0327d09f06c3f42f471b423b01427f21f8
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  hardware:
    dependency: "direct main"
    description:
      path: "../hardware"
      relative: true
    source: path
    version: "0.0.1"
  hashcodes:
    dependency: transitive
    description:
      name: hashcodes
      sha256: "80f9410a5b3c8e110c4b7604546034749259f5d6dcca63e0d3c17c9258f1a651"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: "direct main"
    description:
      name: http
      sha256: "759d1a329847dd0f39226c688d3e06a6b8679668e350e2891a6474f8b4bb8525"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image:
    dependency: transitive
    description:
      name: image
      sha256: f31d52537dc417fdcde36088fdf11d191026fd5e4fae742491ebd40e5a8bea7d
      url: "https://pub.dev"
    source: hosted
    version: "4.3.0"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: "26222b01a0c9a2c8fe02fc90b8208bd3325da5ed1f4a2acabf75939031ac0bdd"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.7"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "42c098e7fb6334746be37cdc30369ade356ed4f14d48b7a0313f95a9159f4321"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.9+5"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: e2423c53a68b579a7c37a1eda967b8ae536c3d98518e5db95ca1fe5719a730a3
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: fadafce49e8569257a0cad56d24438a6fa1f0cbd7ee0af9b631f7492818a4ca3
      url: "https://pub.dev"
    source: hosted
    version: "0.8.9+1"
  image_picker_linux:
    dependency: transitive
    description:
      name: image_picker_linux
      sha256: "4ed1d9bb36f7cd60aa6e6cd479779cc56a4cb4e4de8f49d487b1aaad831300fa"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_macos:
    dependency: transitive
    description:
      name: image_picker_macos
      sha256: "3f5ad1e8112a9a6111c46d0b57a7be2286a9a07fc6e1976fdf5be2bd31d4ff62"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: fa4e815e6fcada50e35718727d83ba1c92f1edf95c0b4436554cec301b56233b
      url: "https://pub.dev"
    source: hosted
    version: "2.9.3"
  image_picker_windows:
    dependency: transitive
    description:
      name: image_picker_windows
      sha256: "6ad07afc4eb1bc25f3a01084d28520496c4a3bb0cb13685435838167c9dcedeb"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+1"
  image_size_getter:
    dependency: transitive
    description:
      name: image_size_getter
      sha256: "7c26937e0ae341ca558b7556591fd0cc456fcc454583b7cb665d2f03e93e590f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  libserialport:
    dependency: "direct main"
    description:
      name: libserialport
      sha256: "392e1592def65282429832ec66fa25e9e163d3b37716b97691482e2406720727"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0+1"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "0a217c6c989d21039f1498c3ed9f3ed71b354e69873f13a8dfc3c9fe76f1b452"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: transitive
    description:
      name: lottie
      sha256: a93542cc2d60a7057255405f62252533f8e8956e7e06754955669fd32fb4b216
      url: "https://pub.dev"
    source: hosted
    version: "2.7.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "1803e76e6653768d64ed8ff2e1e67bea3ad4b923eb5c56a295c3e634bad5960e"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.16"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "9528f2f296073ff54cb9fee677df673ace1218163c3bc7628093e7eed5203d41"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  material_dialogs:
    dependency: "direct main"
    description:
      name: material_dialogs
      sha256: "7828b8e70e5227c1fc2bcdf5c3ac4f22049b9ead6bae96a44ca55a4c7b8da949"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "3c74dbf8763d36539f114c799d8a2d87343b5067e9d796ca22b5eb8437090ee3"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  mqtt_client:
    dependency: transitive
    description:
      name: mqtt_client
      sha256: "930a951a748a3feacd57bde9a608740c724f7afb0e1be1ef56f3e1fe9f689cf9"
      url: "https://pub.dev"
    source: hosted
    version: "10.3.0"
  mysql1:
    dependency: "direct main"
    description:
      name: mysql1
      sha256: "68aec7003d2abc85769bafa1777af3f4a390a90c31032b89636758ff8eb839e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.20.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  oktoast:
    dependency: "direct main"
    description:
      name: oktoast
      sha256: f1366c5c793ddfb8f55bc6fc3e45db43c45debf173b765fb4c5ec096cbdeb84a
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: "direct main"
    description:
      name: path
      sha256: "8829d8a55c13fc0e37127c29fedf290c102f4e40ae94ada574091fe0ff96c917"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.3"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: c9e7d3a4cd1410877472158bee69963a4579f78b68c65a2b7d40d1a7a88bb161
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "51f0d2c554cfbc9d6a312ab35152fc77e2f0b758ce9f1a444a3a1e5b8f3c6b7f"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "5a7999be66e000916500be4f15a3633ebceb8302719b47b9cc49ce924125350f"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: "284a66179cabdf942f838543e10413246f06424d960c92ba95c84439154fcac8"
      url: "https://pub.dev"
    source: hosted
    version: "11.0.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: f9fddd3b46109bd69ff3f9efa5006d2d309b7aec0f3c1c5637a60a2d5659e76e
      url: "https://pub.dev"
    source: hosted
    version: "11.1.0"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: cb3798bef7fc021ac45b308f4b51208a152792445cce0448c9a4ba5879dd8750
      url: "https://pub.dev"
    source: hosted
    version: "5.4.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "12220bb4b65720483f8fa9450b4332347737cf8213dd2840d8b2c823e47243ec"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.4"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  process_run:
    dependency: transitive
    description:
      name: process_run
      sha256: bc3c841708c7ea5afdf810f70b7a3e453b45a11c2d0a0b1073f3ece7ec793dfa
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  provider:
    dependency: "direct main"
    description:
      name: provider
      sha256: "4abbd070a04e9ddc287673bf5a030c7ca8b685ff70218720abab8b092f53dd84"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c799b721d79eb6ee6fa56f00c04b472dcd44a30d258fac2174a6ec57302678f8
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  screen_retriever:
    dependency: transitive
    description:
      name: screen_retriever
      sha256: "6ee02c8a1158e6dae7ca430da79436e3b1c9563c8cf02f524af997c201ac2b90"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.9"
  sea_socket:
    dependency: "direct main"
    description:
      path: "../sea_socket"
      relative: true
    source: path
    version: "0.0.1"
  seaface:
    dependency: "direct main"
    description:
      path: "../seaface"
      relative: true
    source: path
    version: "0.0.1"
  sealog:
    dependency: "direct main"
    description:
      path: "../sealog"
      relative: true
    source: path
    version: "0.0.1"
  seasetting:
    dependency: "direct main"
    description:
      path: "../seasetting"
      relative: true
    source: path
    version: "0.0.1"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: d3bbe5553a986e83980916ded2f0b435ef2e1893dfaa29d5a7a790d0eca12180
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7708d83064f38060c7b39db12aefe449cb8cdc031d6062280087bc4cdb988f5c"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.5"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "22e2ecac9419b4246d7c22bfbbda589e3acf5c0351137d87dd2939d984d37c3b"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqlite3:
    dependency: "direct main"
    description:
      name: sqlite3
      sha256: "281b672749af2edf259fc801f0fcba092257425bcd32a0ce1c8237130bc934c7"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.2"
  sqlite3_flutter_libs:
    dependency: "direct main"
    description:
      name: sqlite3_flutter_libs
      sha256: "2b03273e71867a8a4d030861fc21706200debe5c5858a4b9e58f4a1c129586a4"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.39"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "75760ffd7786fffdfb9597c35c5b27eaeec82be8edfb6d71d32651128ed7aab8"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  time:
    dependency: transitive
    description:
      name: time
      sha256: "370572cf5d1e58adcb3e354c47515da3f7469dac3a95b447117e728e7be6f461"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  universal_html:
    dependency: transitive
    description:
      name: universal_html
      sha256: "56536254004e24d9d8cfdb7dbbf09b74cf8df96729f38a2f5c238163e3d58971"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  universal_io:
    dependency: transitive
    description:
      name: universal_io
      sha256: "1722b2dcc462b4b2f3ee7d188dad008b6eb4c40bbd03a3de451d82c78bba9aad"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: c512655380d241a337521703af62d2c122bf7b77a46ff7dd750092aa9433499c
      url: "https://pub.dev"
    source: hosted
    version: "6.2.4"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: d4ed0711849dd8e33eb2dd69c25db0d0d3fdc37e0a62e629fe32f57a22db2745
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "75bb6fe3f60070407704282a2d295630cab232991eb52542b18347a8a941df03"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.4"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "9a1a42d5d2d95400c795b2914c36fdcb525870c752569438e4ebb09a2b5d90de"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "7fd2f55fe86cea2897b963e864dc01a7eb0719ecc65fcef4c1cc3d686d718bb2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: ecf9725510600aa2bb6d7ddabe16357691b6d2805f66216a97d1b881e21beff7
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: "22c94e5ad1e75f9934b766b53c742572ee2677c56bc871d850a57dad0f82127f"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.2"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: c86987475f162fadff579e7320c7ddda04cd2fdeffbe1129227a85d9ac9e03da
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "12faff3f73b1741a36ca7e31b292ddeb629af819ca9efe9953b70bd63fc8cd81"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.11+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "0b7fd4a0bbc4b92641dbf20adfd7e3fd1398fe17102d94b674234563e110088a"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  web:
    dependency: transitive
    description:
      name: web
      sha256: dc8ccd225a2005c1be616fe02951e2e342092edf968cf0844220383757ef8f10
      url: "https://pub.dev"
    source: hosted
    version: "0.1.4-beta"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webview_windows:
    dependency: transitive
    description:
      name: webview_windows
      sha256: "47fcad5875a45db29dbb5c9e6709bf5c88dcc429049872701343f91ed7255730"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: b0f37db61ba2f2e9b7a78a1caece0052564d1bc70668156cf3a29d676fe4e574
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  window_manager:
    dependency: "direct main"
    description:
      name: window_manager
      sha256: "8699323b30da4cdbe2aa2e7c9de567a6abd8a97d9a5c850a3c86dcd0b34bbfbf"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.9"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: faea9dee56b520b55a566385b84f2e8de55e7496104adada9962e0bd11bcff1d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "5bc72e1e45e941d825fd7468b9b4cc3b9327942649aeb6fc5cdbf135f0a86e84"
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  xml2json:
    dependency: "direct main"
    description:
      name: xml2json
      sha256: "52b7c8d350fdce09545b058982c26689ee89f1eb188cc9910d585665bfe27bc0"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.3"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=3.1.0 <4.0.0"
  flutter: ">=3.13.0"
