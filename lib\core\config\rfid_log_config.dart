/// RFID日志配置
/// 用于控制RFID扫描过程中的日志输出级别
class RFIDLogConfig {
  static final RFIDLogConfig _instance = RFIDLogConfig._internal();
  factory RFIDLogConfig() => _instance;
  RFIDLogConfig._internal();

  static RFIDLogConfig get instance => _instance;

  /// 日志级别枚举
  enum LogLevel {
    /// 静默模式 - 只输出错误
    silent,
    /// 简洁模式 - 只输出关键信息
    minimal,
    /// 标准模式 - 输出重要信息
    standard,
    /// 详细模式 - 输出所有信息（调试用）
    verbose,
  }

  /// 当前日志级别
  LogLevel _currentLevel = LogLevel.minimal;

  /// 获取当前日志级别
  LogLevel get currentLevel => _currentLevel;

  /// 设置日志级别
  void setLogLevel(LogLevel level) {
    _currentLevel = level;
    print('RFID日志级别已设置为: ${level.name}');
  }

  /// 检查是否应该输出指定级别的日志
  bool shouldLog(LogLevel level) {
    return level.index <= _currentLevel.index;
  }

  /// 检查是否应该输出错误日志
  bool get shouldLogError => _currentLevel != LogLevel.silent;

  /// 检查是否应该输出关键信息
  bool get shouldLogMinimal => shouldLog(LogLevel.minimal);

  /// 检查是否应该输出标准信息
  bool get shouldLogStandard => shouldLog(LogLevel.standard);

  /// 检查是否应该输出详细信息
  bool get shouldLogVerbose => shouldLog(LogLevel.verbose);

  /// 条件日志输出
  static void log(String message, {LogLevel level = LogLevel.standard}) {
    if (instance.shouldLog(level)) {
      print(message);
    }
  }

  /// 错误日志
  static void error(String message) {
    if (instance.shouldLogError) {
      print('❌ $message');
    }
  }

  /// 关键信息日志
  static void info(String message) {
    if (instance.shouldLogMinimal) {
      print('ℹ️ $message');
    }
  }

  /// 成功信息日志
  static void success(String message) {
    if (instance.shouldLogMinimal) {
      print('✅ $message');
    }
  }

  /// 警告日志
  static void warning(String message) {
    if (instance.shouldLogMinimal) {
      print('⚠️ $message');
    }
  }

  /// 调试日志
  static void debug(String message) {
    if (instance.shouldLogVerbose) {
      print('🔍 $message');
    }
  }

  /// 从环境变量或配置文件加载日志级别
  void loadFromConfig() {
    // 这里可以从配置文件或环境变量读取日志级别
    // 例如：从SharedPreferences读取用户设置
    
    // 默认使用简洁模式
    _currentLevel = LogLevel.minimal;
    
    // 在调试模式下可以使用详细模式
    assert(() {
      _currentLevel = LogLevel.standard;
      return true;
    }());
  }

  /// 保存日志级别到配置
  Future<void> saveToConfig() async {
    // 这里可以将日志级别保存到SharedPreferences
    // 例如：
    // final prefs = await SharedPreferences.getInstance();
    // await prefs.setString('rfid_log_level', _currentLevel.name);
  }

  /// 获取日志级别的显示名称
  String get currentLevelDisplayName {
    switch (_currentLevel) {
      case LogLevel.silent:
        return '静默模式';
      case LogLevel.minimal:
        return '简洁模式';
      case LogLevel.standard:
        return '标准模式';
      case LogLevel.verbose:
        return '详细模式';
    }
  }

  /// 获取日志级别的描述
  String get currentLevelDescription {
    switch (_currentLevel) {
      case LogLevel.silent:
        return '只输出错误信息';
      case LogLevel.minimal:
        return '只输出关键信息';
      case LogLevel.standard:
        return '输出重要信息';
      case LogLevel.verbose:
        return '输出所有调试信息';
    }
  }
}
