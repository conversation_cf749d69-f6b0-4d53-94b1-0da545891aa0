import 'package:flutter/material.dart';
import '../services/quick_setup.dart';
import '../services/slave_setup.dart';
import '../services/master_slave_extension.dart';
import '../services/master_slave_comm_service.dart';

/// 主从机配置页面
/// 简单的界面配置主机或从机模式
class MasterSlaveConfigPage extends StatefulWidget {
  const MasterSlaveConfigPage({Key? key}) : super(key: key);

  @override
  State<MasterSlaveConfigPage> createState() => _MasterSlaveConfigPageState();
}

class _MasterSlaveConfigPageState extends State<MasterSlaveConfigPage> {
  bool _isMaster = true;
  final TextEditingController _ipController = TextEditingController(text: '************');
  bool _isEnabled = false;
  String _status = '未启用';

  @override
  void initState() {
    super.initState();
    _checkCurrentStatus();
  }

  @override
  void dispose() {
    _ipController.dispose();
    super.dispose();
  }

  void _checkCurrentStatus() {
    final extension = MasterSlaveExtension.instance;
    setState(() {
      _isEnabled = extension.isEnabled;
      if (_isEnabled) {
        _isMaster = extension.isMaster;
        _status = _isMaster ? '主机模式已启用' : '从机模式已启用';
      } else {
        _status = '未启用';
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('主从机配置'),
        backgroundColor: Colors.blue,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 当前状态
            Card(
              color: _isEnabled ? Colors.green.shade50 : Colors.grey.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Icon(
                      _isEnabled ? Icons.check_circle : Icons.radio_button_unchecked,
                      size: 48,
                      color: _isEnabled ? Colors.green : Colors.grey,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _status,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: _isEnabled ? Colors.green : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // 模式选择
            const Text('选择模式', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('主机模式'),
                    subtitle: const Text('连接RFID阅读器'),
                    value: true,
                    groupValue: _isMaster,
                    onChanged: _isEnabled ? null : (value) {
                      setState(() {
                        _isMaster = value!;
                        if (_isMaster) {
                          _ipController.text = '************'; // 从机IP
                        }
                      });
                    },
                  ),
                ),
                Expanded(
                  child: RadioListTile<bool>(
                    title: const Text('从机模式'),
                    subtitle: const Text('连接到主机'),
                    value: false,
                    groupValue: _isMaster,
                    onChanged: _isEnabled ? null : (value) {
                      setState(() {
                        _isMaster = value!;
                        if (!_isMaster) {
                          _ipController.text = '************'; // 主机IP
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 20),
            
            // 🔥 优化：只有从机模式才需要IP地址配置
            if (!_isMaster) ...[
              const Text(
                '主机IP地址',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              TextField(
                controller: _ipController,
                enabled: !_isEnabled,
                decoration: const InputDecoration(
                  hintText: '输入主机IP地址',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.computer),
                ),
              ),
              const SizedBox(height: 20),
            ] else ...[
              // 主机模式显示监听端口信息
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.shade50,
                  border: Border.all(color: Colors.green.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.green.shade600),
                        const SizedBox(width: 8),
                        Text(
                          '主机配置',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('监听端口: 8888'),
                    const Text('等待从机连接...'),
                    const Text('支持多个从机同时连接'),
                  ],
                ),
              ),
              const SizedBox(height: 20),
            ],
            
            const SizedBox(height: 30),
            
            // 操作按钮
            if (!_isEnabled) ...[
              ElevatedButton(
                onPressed: _enableMode,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isMaster ? Colors.green : Colors.orange,
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: Text(
                  _isMaster ? '启用主机模式' : '启用从机模式',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ] else ...[
              ElevatedButton(
                onPressed: _disableMode,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text(
                  '禁用当前模式',
                  style: TextStyle(fontSize: 16),
                ),
              ),
              const SizedBox(height: 12),
              ElevatedButton(
                onPressed: _showStatus,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  minimumSize: const Size(double.infinity, 50),
                ),
                child: const Text(
                  '查看详细状态',
                  style: TextStyle(fontSize: 16),
                ),
              ),
            ],
            
            const SizedBox(height: 30),
            
            // 说明信息
            Card(
              color: Colors.blue.shade50,
              child: const Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('使用说明', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                    SizedBox(height: 8),
                    Text('• 主机模式：连接RFID阅读器，为其他设备提供数据'),
                    Text('• 从机模式：连接到主机，接收共享的扫描数据'),
                    Text('• 确保设备在同一网络且可以互相访问'),
                    Text('• 配置完成后，正常使用出馆功能即可'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _enableMode() async {
    try {
      if (_isMaster) {
        // 🔥 优化：主机模式不需要IP地址
        await QuickSetup.enableMaster();
        _showSnackBar('主机模式启用成功，等待从机连接', Colors.green);
      } else {
        // 从机模式需要主机IP地址
        if (_ipController.text.trim().isEmpty) {
          _showSnackBar('请输入主机IP地址', Colors.orange);
          return;
        }
        await SlaveSetup.enableSlave(masterAddress: _ipController.text.trim());
        _showSnackBar('从机模式启用成功', Colors.orange);
      }
      _checkCurrentStatus();
    } catch (e) {
      _showSnackBar('启用失败: $e', Colors.red);
    }
  }

  void _disableMode() {
    try {
      // 统一使用MasterSlaveExtension的disable方法
      MasterSlaveExtension.instance.disable();
      _showSnackBar('已禁用', Colors.grey);
      _checkCurrentStatus();
    } catch (e) {
      _showSnackBar('禁用失败: $e', Colors.red);
    }
  }

  void _showStatus() {
    final extension = MasterSlaveExtension.instance;
    final status = extension.getStatus();
    final commService = MasterSlaveCommService.instance;
    final connInfo = commService.getConnectionInfo();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('详细状态'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('启用状态: ${status['enabled']}'),
            Text('通道ID: ${status['channel_id']}'),
            Text('模式: ${status['is_master'] ? '主机' : '从机'}'),
            Text('共享池: ${status['shared_pool_size']}个条码'),
            Text('队列: ${status['queue_size']}个条码'),
            const SizedBox(height: 8),
            const Text('网络连接:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('连接状态: ${connInfo['is_connected']}'),
            if (connInfo['is_master'] == true) ...[
              Text('连接的从机: ${connInfo['connected_slaves']}台'),
              if (connInfo['slave_list'] != null && (connInfo['slave_list'] as List).isNotEmpty)
                ...((connInfo['slave_list'] as List).map((slave) => Text('  - $slave'))),
            ] else ...[
              Text('主机地址: ${connInfo['master_address'] ?? 'N/A'}'),
            ],
            Text('端口: ${connInfo['port']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
