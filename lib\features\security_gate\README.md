# 安全闸机主从机扩展系统

## 🎯 系统概述

本系统为现有的安全闸机系统添加了主从机扩展功能，实现了两个通道共用一个RFID阅读器的需求。通过扩展现有架构而不破坏原有功能，确保系统的稳定性和兼容性。

## 🏗️ 架构设计

```
现有GateCoordinator (保持不变)
    │
    │ 事件监听和扩展
    ▼
MasterSlaveExtension (新增扩展层)
    │
    ├── SharedScanPoolService (共享扫描池)
    ├── ChannelQueueService (通道队列)
    └── MasterSlaveCommService (主从机通信)
```

### 核心组件

1. **MasterSlaveExtension** - 主从机扩展管理器（核心）
2. **SharedScanPoolService** - 共享扫描池服务（列表1）
3. **ChannelQueueService** - 通道处理队列服务（列表2）
4. **MasterSlaveCommService** - 主从机通信服务
5. **GateConfigService** - 配置管理服务

## 📊 数据流设计

### 列表1：共享扫描池
- **管理者**：主机
- **访问者**：从机（通过网络）
- **特点**：自动去重，实时同步
- **用途**：存储RFID阅读器扫描到的所有条码

### 列表2：通道处理队列
- **管理者**：各通道独立
- **特点**：时间窗口收集，自动去重
- **用途**：存储本通道时间窗口内的条码

## 🔄 工作流程

### 出馆流程

1. **出馆开始**
   - 清空共享扫描池（列表1）
   - 清空通道处理队列（列表2）
   - 开始定时收集数据
   - 主机启动RFID扫描

2. **扫描阶段**
   - RFID阅读器持续扫描，填充列表1
   - 通道定时从列表1收集数据到列表2
   - 主从机实时同步列表1

3. **到达位置**
   - 停止数据收集
   - 主机停止RFID扫描
   - 延迟1秒后处理书籍信息

4. **书籍检查**
   - 根据列表2中的条码查询书籍信息
   - 判断是否有未借书籍
   - 决定是否允许出馆

5. **出馆结束**
   - 清理状态
   - 回到空闲状态

## 🚀 使用方法

### 1. 启用主机扩展（通道1）

```dart
// 配置为主机模式
await GateConfigService.instance.configureAsMaster(
  channelId: 'channel_1',
  slaveAddress: '*************', // 从机IP
  port: 8888,
);

// 初始化扩展
await GateConfigService.instance.initialize();

// 获取扩展管理器
final extension = GateConfigService.instance.extension;
```

### 2. 启用从机扩展（通道2）

```dart
// 配置为从机模式
await GateConfigService.instance.configureAsSlave(
  channelId: 'channel_2',
  masterAddress: '************', // 主机IP
  port: 8888,
);

// 初始化扩展
await GateConfigService.instance.initialize();

// 获取扩展管理器
final extension = GateConfigService.instance.extension;
```

### 3. 扩展自动集成

扩展系统会自动监听现有GateCoordinator的事件：
- `exitStart` → 清空共享池，开始收集
- `positionReached` → 停止收集，获取条码列表
- `exitEnd` → 清理状态

**无需修改现有代码**，扩展会自动工作！

## ✅ 设计优势

### 1. 🔄 无侵入式扩展
- 不修改现有GateCoordinator代码
- 通过事件监听实现功能扩展
- 保持原有系统稳定性

### 2. ⏰ 时间窗口隔离
- 每个通道独立的时间窗口
- 确保数据准确性，不会少也不会多太多

### 3. 🔁 自动去重机制
- 使用Set数据结构天然去重
- 避免重复处理相同条码

### 4. 📡 实时数据同步
- 主从机实时同步共享池
- 确保数据一致性

### 5. 🛡️ 故障隔离
- 通道间相互独立
- 扩展故障不影响原有功能

### 6. 🔧 配置灵活
- 支持动态启用/禁用扩展
- 配置持久化保存

## 🔧 配置说明

### 网络配置
- 默认端口：8888
- 通信协议：TCP Socket
- 数据格式：JSON

### 时间配置
- 数据收集间隔：100ms
- 到达位置延迟：1秒
- 同步间隔：200ms

## 📈 监控和调试

### 系统状态监控
```dart
// 获取系统状态
final stats = controller.getStatistics();
print('当前状态: ${stats['current_state']}');
print('队列大小: ${stats['queue_stats']['queue_size']}');
print('共享池大小: ${stats['pool_stats']['pool_size']}');
```

### 错误处理
```dart
// 监听错误
controller.errorStream.listen((error) {
  print('系统错误: $error');
  // 根据错误类型进行处理
});
```

## 🔍 故障排除

### 常见问题

1. **通信连接失败**
   - 检查网络配置
   - 确认IP地址和端口
   - 检查防火墙设置

2. **RFID扫描异常**
   - 检查阅读器连接
   - 确认网口配置
   - 查看错误日志

3. **数据同步问题**
   - 检查主从机连接状态
   - 确认数据格式正确
   - 重启通信服务

## 📝 注意事项

1. **并发处理**：系统设计避免了真正的并发冲突，因为物理上一个人只能同时通过一个通道

2. **数据完整性**：通过时间窗口机制确保每个通道获取到完整的扫描数据

3. **网络稳定性**：建议使用有线网络连接，确保主从机通信稳定

4. **配置持久化**：系统配置会自动保存，重启后自动恢复

## 🚀 快速开始

查看 `examples/gate_usage_example.dart` 文件，其中包含了完整的使用示例和最佳实践。
