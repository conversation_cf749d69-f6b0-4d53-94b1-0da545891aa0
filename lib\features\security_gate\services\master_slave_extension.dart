import 'dart:async';
import 'package:flutter/foundation.dart';
import 'shared_scan_pool_service.dart';
import 'channel_queue_service.dart';
import 'master_slave_comm_service.dart';
import 'gate_coordinator.dart';
import '../models/gate_state.dart';
import '../models/gate_event.dart';

/// 主从机扩展管理器
/// 🔥 为现有GateCoordinator添加主从机支持
/// 作为现有架构的扩展，不破坏原有功能
class MasterSlaveExtension {
  static final _instance = MasterSlaveExtension._internal();
  static MasterSlaveExtension get instance => _instance;
  MasterSlaveExtension._internal();
  
  // 配置信息
  String? _channelId;
  bool _isMaster = false;
  bool _isEnabled = false;
  
  // 服务实例
  SharedScanPoolService? _sharedPool;
  ChannelQueueService? _channelQueue;
  MasterSlaveCommService? _commService;
  
  // 订阅管理
  StreamSubscription? _gateEventSubscription;
  StreamSubscription? _poolSubscription;
  StreamSubscription? _commSubscription;

  /// 获取当前配置
  bool get isMaster => _isMaster;
  String? get channelId => _channelId;
  bool get isEnabled => _isEnabled;

  /// 启用主从机扩展
  Future<void> enable({
    required String channelId,
    required bool isMaster,
    String? slaveAddress,
    String? masterAddress,
    int port = 8888,
  }) async {
    try {
      debugPrint('启用主从机扩展: $channelId (${isMaster ? "主机" : "从机"})');
      
      _channelId = channelId;
      _isMaster = isMaster;
      
      // 1. 初始化共享扫描池
      _sharedPool = SharedScanPoolService.instance;
      await _sharedPool!.initializeWithRFID();
      
      // 2. 初始化通道队列
      _channelQueue = ChannelQueueService(channelId);
      
      // 3. 初始化通信服务
      _commService = MasterSlaveCommService.instance;
      if (isMaster) {
        // 🔥 优化：主机模式不需要从机地址
        await _commService!.configureAsMaster(
          port: port,
        );
      } else if (!isMaster && masterAddress != null) {
        await _commService!.configureAsSlave(
          masterAddress: masterAddress,
          port: port,
        );
      }
      
      // 4. 监听现有GateCoordinator的事件
      _setupGateCoordinatorIntegration();
      
      _isEnabled = true;
      debugPrint('主从机扩展启用成功');
      
    } catch (e) {
      debugPrint('启用主从机扩展失败: $e');
      rethrow;
    }
  }

  /// 禁用主从机扩展
  void disable() {
    debugPrint('禁用主从机扩展');
    
    _gateEventSubscription?.cancel();
    _poolSubscription?.cancel();
    _commSubscription?.cancel();
    
    _gateEventSubscription = null;
    _poolSubscription = null;
    _commSubscription = null;
    
    _channelQueue?.dispose();
    _commService?.disconnect();
    
    _channelQueue = null;
    _commService = null;
    _sharedPool = null;
    
    _isEnabled = false;
    _channelId = null;
  }

  /// 集成现有GateCoordinator
  void _setupGateCoordinatorIntegration() {
    try {
      final coordinator = GateCoordinator.instance;

      // 监听闸机事件
      _gateEventSubscription = coordinator.eventStream.listen(
        (event) {
          _handleGateEvent(event);
        },
        onError: (error) {
          debugPrint('[$_channelId] 监听闸机事件出错: $error');
        },
      );

      debugPrint('[$_channelId] 已集成现有GateCoordinator，开始监听事件');
    } catch (e) {
      debugPrint('[$_channelId] 集成GateCoordinator失败: $e');
    }
  }

  /// 处理闸机事件
  void _handleGateEvent(GateEvent event) {
    if (!_isEnabled) return;

    debugPrint('[$_channelId] 收到闸机事件: ${event.type}');

    switch (event.type) {
      case 'exit_start': // 使用正确的事件类型
        _handleExitStart();
        break;
      case 'position_reached': // 自定义事件类型
        _handlePositionReached();
        break;
      case 'exit_end': // 使用正确的事件类型
        _handleExitEnd();
        break;
    }
  }

  /// 处理出馆开始
  void _handleExitStart() {
    debugPrint('[$_channelId] 主从机扩展：处理出馆开始');

    // 🔥 重要：不要清空共享池，因为RFID可能已经开始扫描
    // 只清空通道队列，然后开始收集
    _channelQueue?.clearQueue();
    _channelQueue?.startCollecting();

    debugPrint('[$_channelId] 主从机扩展：开始收集数据');
  }

  /// 处理到达指定位置
  void _handlePositionReached() {
    debugPrint('[$_channelId] 主从机扩展：处理到达指定位置');
    
    _channelQueue?.stopCollecting();
    
    // 延迟1秒后获取收集的条码
    Timer(const Duration(seconds: 1), () {
      final collectedBarcodes = _channelQueue?.getProcessQueue() ?? <String>{};
      debugPrint('[$_channelId] 收集到${collectedBarcodes.length}个条码: $collectedBarcodes');
      
      // 这里可以将收集到的条码传递给现有的书籍检查逻辑
      _notifyCollectedBarcodes(collectedBarcodes.toList());
    });
  }

  /// 处理出馆结束
  void _handleExitEnd() {
    debugPrint('[$_channelId] 主从机扩展：处理出馆结束');
    
    _channelQueue?.clearQueue();
  }

  /// 通知收集到的条码
  void _notifyCollectedBarcodes(List<String> barcodes) {
    // 这里可以与现有的GateCoordinator集成
    // 例如：直接调用现有的书籍检查逻辑
    debugPrint('[$_channelId] 通知收集到的条码: $barcodes');
    
    // TODO: 集成现有的书籍检查逻辑
    // 可以通过调用GateCoordinator的私有方法或者发送自定义事件
  }

  /// 手动触发出馆开始事件
  void triggerExitStart() {
    if (_isEnabled) {
      _handleExitStart();
    }
  }

  /// 手动触发位置到达事件（用于测试）
  void triggerPositionReached() {
    if (_isEnabled) {
      _handlePositionReached();
    }
  }

  /// 手动添加条码到共享池（用于测试）
  void addTestBarcode(String barcode) {
    if (_isEnabled && _isMaster) {
      _sharedPool?.addBarcode(barcode);
    }
  }

  /// 获取当前状态信息
  Map<String, dynamic> getStatus() {
    return {
      'enabled': _isEnabled,
      'channel_id': _channelId,
      'is_master': _isMaster,
      'shared_pool_size': _sharedPool?.poolSize ?? 0,
      'queue_size': _channelQueue?.queueSize ?? 0,
      'comm_connected': _commService?.isConnected ?? false,
    };
  }

  /// 获取收集到的条码（用于调试）
  List<String> getCollectedBarcodes() {
    return _channelQueue?.getProcessQueue().toList() ?? [];
  }

  /// 获取共享池条码（用于调试）
  List<String> getSharedPoolBarcodes() {
    return _sharedPool?.getCurrentPool().toList() ?? [];
  }

  /// 清理资源
  void dispose() {
    disable();
  }
}

/// 简化的配置类
class MasterSlaveConfig {
  final String channelId;
  final bool isMaster;
  final String? slaveAddress;
  final String? masterAddress;
  final int port;

  MasterSlaveConfig({
    required this.channelId,
    required this.isMaster,
    this.slaveAddress,
    this.masterAddress,
    this.port = 8888,
  });

  /// 从JSON创建
  factory MasterSlaveConfig.fromJson(Map<String, dynamic> json) {
    return MasterSlaveConfig(
      channelId: json['channel_id'] ?? '',
      isMaster: json['is_master'] ?? false,
      slaveAddress: json['slave_address'],
      masterAddress: json['master_address'],
      port: json['port'] ?? 8888,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'channel_id': channelId,
      'is_master': isMaster,
      'slave_address': slaveAddress,
      'master_address': masterAddress,
      'port': port,
    };
  }

  /// 验证配置
  bool isValid() {
    if (channelId.isEmpty) return false;

    if (isMaster) {
      return slaveAddress != null && slaveAddress!.isNotEmpty;
    } else {
      return masterAddress != null && masterAddress!.isNotEmpty;
    }
  }

  @override
  String toString() {
    return 'MasterSlaveConfig(channelId: $channelId, isMaster: $isMaster, '
           'slaveAddress: $slaveAddress, masterAddress: $masterAddress, port: $port)';
  }
}
