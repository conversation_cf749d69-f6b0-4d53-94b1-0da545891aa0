import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'shared_scan_pool_service.dart';

/// 主从机通信服务
/// 处理主从机间的数据同步
class MasterSlaveCommService {
  static final _instance = MasterSlaveCommService._internal();
  static MasterSlaveCommService get instance => _instance;
  MasterSlaveCommService._internal();

  // 配置信息
  bool _isMaster = false;
  String? _masterAddress;
  int _port = 8888;

  // 网络组件
  ServerSocket? _server;
  Socket? _clientSocket;

  // 🔥 新增：多从机连接管理
  final List<Socket> _connectedSlaves = [];
  final Map<Socket, String> _slaveInfo = {}; // 存储从机信息
  
  // 连接状态
  bool _isConnected = false;
  
  // 数据同步定时器
  Timer? _syncTimer;
  
  // 事件流
  final StreamController<String> _messageController = 
      StreamController<String>.broadcast();
  final StreamController<String> _errorController = 
      StreamController<String>.broadcast();
  final StreamController<bool> _connectionController = 
      StreamController<bool>.broadcast();

  /// 获取消息流
  Stream<String> get messageStream => _messageController.stream;
  
  /// 获取错误流
  Stream<String> get errorStream => _errorController.stream;
  
  /// 获取连接状态流
  Stream<bool> get connectionStream => _connectionController.stream;
  
  /// 是否为主机
  bool get isMaster => _isMaster;
  
  /// 是否已连接
  bool get isConnected => _isConnected;

  /// 配置为主机模式
  Future<void> configureAsMaster({
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为主机模式，监听端口: $port');

      _isMaster = true;
      _port = port;

      // 启动服务器
      await _startServer();

      // 开始定时同步数据到从机
      _startSyncTimer();

      debugPrint('主机模式配置完成');
    } catch (e) {
      final errorMsg = '配置主机模式失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 配置为从机模式
  Future<void> configureAsSlave({
    required String masterAddress,
    int port = 8888,
  }) async {
    try {
      debugPrint('配置为从机模式: $masterAddress:$port');
      
      _isMaster = false;
      _masterAddress = masterAddress;
      _port = port;
      
      // 连接到主机
      await _connectToMaster();
      
      debugPrint('从机模式配置完成');
    } catch (e) {
      final errorMsg = '配置从机模式失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 启动服务器（主机模式）
  Future<void> _startServer() async {
    try {
      _server = await ServerSocket.bind(InternetAddress.anyIPv4, _port);
      debugPrint('主机服务器启动成功，监听端口: $_port');
      
      _server!.listen((Socket client) {
        final clientInfo = '${client.remoteAddress}:${client.remotePort}';
        debugPrint('新从机连接: $clientInfo');

        // 🔥 新增：添加到从机列表
        _connectedSlaves.add(client);
        _slaveInfo[client] = clientInfo;

        // 更新连接状态
        _isConnected = _connectedSlaves.isNotEmpty;
        _connectionController.add(_isConnected);

        debugPrint('当前连接的从机数量: ${_connectedSlaves.length}');

        // 监听从机消息
        client.listen(
          (data) => _handleSlaveMessage(client, utf8.decode(data)),
          onDone: () {
            debugPrint('从机断开连接: $clientInfo');
            _removeSlaveConnection(client);
          },
          onError: (error) {
            debugPrint('从机连接错误: $clientInfo - $error');
            _errorController.add('从机连接错误: $clientInfo - $error');
            _removeSlaveConnection(client);
          },
        );

        // 🔥 新增：向新连接的从机发送当前状态
        _sendCurrentStateToSlave(client);
      });
    } catch (e) {
      throw Exception('启动服务器失败: $e');
    }
  }

  /// 连接到主机（从机模式）
  Future<void> _connectToMaster() async {
    try {
      _clientSocket = await Socket.connect(_masterAddress!, _port);
      _isConnected = true;
      _connectionController.add(true);
      
      debugPrint('成功连接到主机: $_masterAddress:$_port');
      
      // 监听主机消息
      _clientSocket!.listen(
        (data) => _handleServerMessage(utf8.decode(data)),
        onDone: () {
          debugPrint('与主机断开连接');
          _isConnected = false;
          _connectionController.add(false);
          _clientSocket = null;
        },
        onError: (error) {
          debugPrint('主机连接错误: $error');
          _errorController.add('主机连接错误: $error');
        },
      );
    } catch (e) {
      throw Exception('连接主机失败: $e');
    }
  }

  /// 开始定时同步（主机模式）
  void _startSyncTimer() {
    _syncTimer = Timer.periodic(const Duration(milliseconds: 200), (timer) {
      if (_isConnected && _isMaster) {
        _syncPoolToSlave();
      }
    });
  }

  /// 🔥 优化：同步共享池到所有从机（主机模式）
  Future<void> _syncPoolToSlave() async {
    if (!_isMaster || _connectedSlaves.isEmpty) return;

    try {
      final pool = SharedScanPoolService.instance.getCurrentPool();
      final message = {
        'type': 'sync_pool',
        'data': pool.toList(),
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };

      // 🔥 新增：广播到所有从机
      _broadcastToAllSlaves(message);

      // 不要频繁打印，只在数据变化时打印
      if (pool.isNotEmpty) {
        debugPrint('同步${pool.length}个条码到${_connectedSlaves.length}个从机');
      }
    } catch (e) {
      debugPrint('同步数据到从机失败: $e');
      _errorController.add('同步数据失败: $e');
    }
  }

  /// 请求主机清空共享池（从机模式）
  Future<void> requestMasterClearPool() async {
    if (_isMaster || !_isConnected || _clientSocket == null) return;
    
    try {
      final message = {
        'type': 'clear_pool_request',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      };
      
      final jsonStr = jsonEncode(message);
      _clientSocket!.write('$jsonStr\n');
      
      debugPrint('请求主机清空共享池');
    } catch (e) {
      debugPrint('请求清空共享池失败: $e');
      _errorController.add('请求清空共享池失败: $e');
    }
  }

  /// 🔥 新增：移除从机连接
  void _removeSlaveConnection(Socket client) {
    final clientInfo = _slaveInfo[client];
    _connectedSlaves.remove(client);
    _slaveInfo.remove(client);

    // 更新连接状态
    _isConnected = _connectedSlaves.isNotEmpty;
    _connectionController.add(_isConnected);

    debugPrint('从机已移除: $clientInfo，当前连接数: ${_connectedSlaves.length}');

    try {
      client.close();
    } catch (e) {
      debugPrint('关闭从机连接失败: $e');
    }
  }

  /// 🔥 新增：向指定从机发送当前状态
  void _sendCurrentStateToSlave(Socket client) {
    try {
      final sharedPool = SharedScanPoolService.instance;
      final currentBarcodes = sharedPool.getCurrentPool().toList();

      if (currentBarcodes.isNotEmpty) {
        final message = {
          'type': 'sync_barcodes',
          'data': {'barcodes': currentBarcodes},
          'timestamp': DateTime.now().toIso8601String(),
        };

        client.write('${jsonEncode(message)}\n');
        debugPrint('向新从机同步当前状态: ${currentBarcodes.length}个条码');
      }
    } catch (e) {
      debugPrint('向从机发送当前状态失败: $e');
    }
  }

  /// 🔥 新增：广播消息到所有从机
  void _broadcastToAllSlaves(Map<String, dynamic> message) {
    if (_connectedSlaves.isEmpty) return;

    final jsonStr = '${jsonEncode(message)}\n';
    final failedSlaves = <Socket>[];

    for (final slave in _connectedSlaves) {
      try {
        slave.write(jsonStr);
      } catch (e) {
        final clientInfo = _slaveInfo[slave] ?? 'unknown';
        debugPrint('向从机发送消息失败: $clientInfo - $e');
        failedSlaves.add(slave);
      }
    }

    // 移除发送失败的从机连接
    for (final failedSlave in failedSlaves) {
      _removeSlaveConnection(failedSlave);
    }
  }

  /// 🔥 新增：处理从机消息（支持多从机）
  void _handleSlaveMessage(Socket client, String message) {
    final clientInfo = _slaveInfo[client] ?? 'unknown';
    debugPrint('收到从机消息: $clientInfo - $message');

    // 转发给原有的处理方法
    _handleClientMessage(message);
  }

  /// 处理客户端消息（主机模式）
  void _handleClientMessage(String message) {
    try {
      final lines = message.trim().split('\n');
      for (String line in lines) {
        if (line.isEmpty) continue;
        
        final data = jsonDecode(line);
        final type = data['type'] as String?;
        
        switch (type) {
          case 'clear_pool_request':
            debugPrint('收到从机清空共享池请求');
            SharedScanPoolService.instance.clearPool();
            break;
          default:
            debugPrint('未知的从机消息类型: $type');
        }
      }
    } catch (e) {
      debugPrint('处理从机消息失败: $e');
      _errorController.add('处理从机消息失败: $e');
    }
  }

  /// 处理服务器消息（从机模式）
  void _handleServerMessage(String message) {
    try {
      final lines = message.trim().split('\n');
      for (String line in lines) {
        if (line.isEmpty) continue;
        
        final data = jsonDecode(line);
        final type = data['type'] as String?;
        
        switch (type) {
          case 'sync_pool':
            final List<dynamic> poolData = data['data'] ?? [];
            final Set<String> newPool = poolData.cast<String>().toSet();
            SharedScanPoolService.instance.syncPool(newPool);
            break;
          default:
            debugPrint('未知的主机消息类型: $type');
        }
      }
    } catch (e) {
      debugPrint('处理主机消息失败: $e');
      _errorController.add('处理主机消息失败: $e');
    }
  }

  /// 发送自定义消息
  Future<void> sendMessage(Map<String, dynamic> message) async {
    if (!_isConnected || _clientSocket == null) {
      throw Exception('未连接到对端');
    }
    
    try {
      final jsonStr = jsonEncode(message);
      _clientSocket!.write('$jsonStr\n');
      debugPrint('发送消息: ${message['type']}');
    } catch (e) {
      debugPrint('发送消息失败: $e');
      _errorController.add('发送消息失败: $e');
      rethrow;
    }
  }

  /// 重新连接
  Future<void> reconnect() async {
    try {
      await disconnect();
      
      if (_isMaster) {
        await _startServer();
        _startSyncTimer();
      } else {
        await _connectToMaster();
      }
      
      debugPrint('重新连接成功');
    } catch (e) {
      final errorMsg = '重新连接失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      rethrow;
    }
  }

  /// 断开连接
  Future<void> disconnect() async {
    try {
      _isConnected = false;
      _connectionController.add(false);
      
      _syncTimer?.cancel();
      _syncTimer = null;
      
      await _clientSocket?.close();
      _clientSocket = null;
      
      await _server?.close();
      _server = null;
      
      debugPrint('连接已断开');
    } catch (e) {
      debugPrint('断开连接失败: $e');
    }
  }

  /// 获取连接状态信息
  Map<String, dynamic> getConnectionInfo() {
    return {
      'is_master': _isMaster,
      'is_connected': _isConnected,
      'connected_slaves': _connectedSlaves.length,
      'slave_list': _slaveInfo.values.toList(),
      'master_address': _masterAddress,
      'port': _port,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 清理资源
  void dispose() {
    disconnect();
    _messageController.close();
    _errorController.close();
    _connectionController.close();
  }
}
