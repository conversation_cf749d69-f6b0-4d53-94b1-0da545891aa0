import 'dart:developer' as developer;

/// RFID日志过滤器
/// 用于过滤和简化RFID扫描过程中的详细日志输出
class RFIDLogFilter {
  static final RFIDLogFilter _instance = RFIDLogFilter._internal();
  factory RFIDLogFilter() => _instance;
  RFIDLogFilter._internal();

  static RFIDLogFilter get instance => _instance;

  // 需要完全过滤的日志关键词
  static const List<String> _blockedKeywords = [
    '🔧 LSGate使用缓冲模式获取RFID事件记录',
    '📊 原始数据',
    'bytes):',
    '🔍 LSGControlCenter数据结构分析',
    '数据总长度:',
    '长度字节',
    '15962数据起始位置',
    '15962数据长度',
    '🔍 _decodeTagData调试信息',
    '输入data:',
    '输入uid:',
    '数据长度:',
    '🔍 LSGate使用解码器',
    '🔍 设备类型:',
    '🔍 开始调用解码器',
    '🔍 解码器类型:',
    '🔍 解码结果分析',
    'barCode: null',
    'oidList:',
    'OID[',
    'compressMode:',
    'originHexStr:',
    'originData:',
    'isKeepInTag:',
    '🔍 提取的15962数据:',
    '🏷️ 标签UID:',
    '🏷️ 标签原始数据:',
    '📋 LSGControlCenter事件信息:',
    '📅 时间戳:',
    '🔍 开始提取LSGControlCenter标签数据',
    '🔍 LSGControlCenter标签数据提取完成',
    '🎯 添加标签到结果列表',
    '📋 获取第一个报告',
    '📋 获取下一个报告',
    '🔄 处理第',
    '📝 解析报告结果',
    '✅ 数据解析成功',
    '📊 LSGate扫描完成',
    '📋 标签1:',
    '🔍 LSGate ReaderManager收到扫描结果',
    '🏷️ LSGate UID[',
    'data:4106071C30C30C75',
    'coder:Coder15962Std',
    'parseRet：',
    'decoder:15962标准协议',
  ];

  // 需要简化的日志关键词及其简化版本
  static const Map<String, String> _simplifyRules = {
    '🧹 LSGate开始新的扫描流程，清空历史缓存': 'RFID扫描开始',
    '✅ LSGate缓存清空完成，后续扫描将累积标签数据': 'RFID缓存已清空',
    '🔍 RDR_BuffMode_ClearRecords调用结果: ret=0': 'RFID缓存清空成功',
    '🔍 RDR_BuffMode_FetchRecords调用结果: ret=0': 'RFID数据获取成功',
    '📊 LSGate扫描结果: 发现': 'RFID扫描: 发现',
    '📋 开始处理': '处理',
    '🎯 HWTagProvider.changeAddedItem: 收到': 'RFID: 收到',
    '🏷️ 新标签[0]: uid=': '新标签: ',
    '📡 LSGate标签详情:': '标签详情:',
    '🎯 HWTagProvider: 当前总标签数': 'RFID: 总标签数',
  };

  /// 过滤日志消息
  /// 返回null表示应该完全过滤掉这条日志
  /// 返回字符串表示简化后的日志内容
  String? filterLog(String message) {
    // 检查是否需要完全过滤
    for (String keyword in _blockedKeywords) {
      if (message.contains(keyword)) {
        return null; // 完全过滤
      }
    }

    // 检查是否需要简化
    for (String pattern in _simplifyRules.keys) {
      if (message.contains(pattern)) {
        return message.replaceAll(pattern, _simplifyRules[pattern]!);
      }
    }

    // 特殊处理：长UID简化
    if (message.contains('4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000')) {
      return message.replaceAll(
        '4106071C30C30C750201B80304F022000F05011016013067013000000000000000000000000000',
        '********'
      );
    }

    // 特殊处理：条码提取简化
    if (message.contains('从barCode字段提取条码:') || message.contains('从oidList提取条码:')) {
      final regex = RegExp(r'从\w+字段?提取条码: (\w+)');
      final match = regex.firstMatch(message);
      if (match != null) {
        return '提取条码: ${match.group(1)}';
      }
    }

    // 特殊处理：书籍信息请求
    if (message.contains('强制实时获取书籍信息（不使用任何缓存）:')) {
      final regex = RegExp(r'强制实时获取书籍信息（不使用任何缓存）: (\w+)');
      final match = regex.firstMatch(message);
      if (match != null) {
        return '查询书籍: ${match.group(1)}';
      }
    }

    // 特殊处理：SIP2请求
    if (message.contains('请求书籍信息:') && message.contains('第1次尝试')) {
      final regex = RegExp(r'请求书籍信息: (\w+)');
      final match = regex.firstMatch(message);
      if (match != null) {
        return '查询书籍信息: ${match.group(1)}';
      }
    }

    return message; // 保持原样
  }

  /// 自定义日志输出函数
  /// 替代debugPrint使用
  static void log(String message, {String? name}) {
    final filtered = instance.filterLog(message);
    if (filtered != null) {
      if (name != null) {
        developer.log(filtered, name: name);
      } else {
        developer.log(filtered);
      }
    }
  }

  /// 启用日志过滤
  /// 在应用启动时调用
  static void enableFiltering() {
    // 这里可以添加全局日志拦截逻辑
    // 由于Flutter的debugPrint是全局的，我们主要通过替换调用来实现过滤
    developer.log('RFID日志过滤器已启用');
  }

  /// 禁用日志过滤（用于调试）
  static void disableFiltering() {
    developer.log('RFID日志过滤器已禁用');
  }
}
