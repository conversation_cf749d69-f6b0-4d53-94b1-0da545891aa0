import 'package:flutter/foundation.dart';
import 'gate_config_service.dart';
import 'master_slave_extension.dart';
import 'debug_helper.dart';

/// 快速设置主从机扩展
class QuickSetup {
  
  /// 快速启用主机模式（使用您的IP）
  static Future<void> enableMaster() async {
    try {
      debugPrint('🚀 快速启用主机模式...');
      
      await GateConfigService.instance.configureAsMaster(
        channelId: 'channel_1',
        port: 8888,
      );
      
      await GateConfigService.instance.initialize();
      
      debugPrint('✅ 主机模式启用成功！');
      debugPrint('📊 状态: ${MasterSlaveExtension.instance.getStatus()}');
      
    } catch (e) {
      debugPrint('❌ 启用主机失败: $e');
    }
  }
  
  /// 快速启用从机模式
  static Future<void> enableSlave() async {
    try {
      debugPrint('🚀 快速启用从机模式...');
      
      await GateConfigService.instance.configureAsSlave(
        channelId: 'channel_2',
        masterAddress: '************', // 您的IP
        port: 8888,
      );
      
      await GateConfigService.instance.initialize();
      
      debugPrint('✅ 从机模式启用成功！');
      debugPrint('📊 状态: ${MasterSlaveExtension.instance.getStatus()}');
      
    } catch (e) {
      debugPrint('❌ 启用从机失败: $e');
    }
  }
  
  /// 禁用扩展
  static void disable() {
    debugPrint('🛑 禁用主从机扩展...');
    MasterSlaveExtension.instance.disable();
    debugPrint('✅ 扩展已禁用');
  }
  
  /// 检查状态
  static void checkStatus() {
    final extension = MasterSlaveExtension.instance;
    final status = extension.getStatus();
    
    debugPrint('📊 当前状态:');
    debugPrint('  启用: ${status['enabled']}');
    debugPrint('  通道: ${status['channel_id']}');
    debugPrint('  主机: ${status['is_master']}');
    debugPrint('  共享池: ${status['shared_pool_size']}个条码');
    debugPrint('  队列: ${status['queue_size']}个条码');
    debugPrint('  连接: ${status['comm_connected']}');
  }
  
  /// 添加测试条码（仅用于调试，正常情况下RFID会自动扫描）
  static void addTestBarcodes() {
    final extension = MasterSlaveExtension.instance;

    if (!extension.isEnabled || !extension.isMaster) {
      debugPrint('❌ 请先启用主机模式');
      return;
    }

    debugPrint('📚 添加测试条码（调试用）...');
    extension.addTestBarcode('TEST001');
    extension.addTestBarcode('TEST002');

    debugPrint('✅ 已添加2个测试条码（调试用）');
    debugPrint('📊 共享池: ${extension.getSharedPoolBarcodes()}');
  }
  
  /// 模拟位置到达
  static void simulatePositionReached() {
    final extension = MasterSlaveExtension.instance;

    if (!extension.isEnabled) {
      debugPrint('❌ 请先启用扩展');
      return;
    }

    debugPrint('📍 模拟位置到达...');
    extension.triggerPositionReached();

    // 延迟查看结果
    Future.delayed(const Duration(seconds: 2), () {
      debugPrint('📊 收集结果: ${extension.getCollectedBarcodes()}');
    });
  }

  /// 运行完整测试
  static Future<void> runFullTest() async {
    await DebugHelper.testMasterSlaveFlow();
  }

  /// 开始事件监听
  static void startEventMonitoring() {
    DebugHelper.startEventMonitoring();
  }
}
