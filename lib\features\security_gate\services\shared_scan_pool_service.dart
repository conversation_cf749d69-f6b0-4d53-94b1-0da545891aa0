import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'rfid_service.dart';
import '../models/book_scan_result.dart';

/// 共享扫描池服务 - 管理列表1
/// 集成现有RFID服务，主机负责填充，从机通过网络访问
class SharedScanPoolService {
  static final _instance = SharedScanPoolService._internal();
  static SharedScanPoolService get instance => _instance;
  SharedScanPoolService._internal();

  // 列表1：共享扫描池（自动去重）
  final Set<String> _scanPool = <String>{};

  // 扫描池变化流
  final StreamController<Set<String>> _poolController =
      StreamController<Set<String>>.broadcast();

  // 错误流
  final StreamController<String> _errorController =
      StreamController<String>.broadcast();

  // 🔥 集成现有RFID服务
  RFIDService? _rfidService;
  StreamSubscription? _rfidBarcodeSubscription;

  /// 获取扫描池变化流
  Stream<Set<String>> get poolStream => _poolController.stream;
  
  /// 获取错误流
  Stream<String> get errorStream => _errorController.stream;
  
  /// 获取当前扫描池大小
  int get poolSize => _scanPool.length;
  
  /// 获取当前扫描池快照
  Set<String> getCurrentPool() {
    return Set<String>.from(_scanPool);
  }

  /// 🔥 集成现有RFID服务
  Future<void> initializeWithRFID() async {
    try {
      _rfidService = RFIDService.instance;

      // 监听现有RFID服务的条码流
      _rfidBarcodeSubscription = _rfidService!.barcodeStream.listen((barcode) {
        addBarcode(barcode);
      });

      debugPrint('共享扫描池已集成现有RFID服务');
    } catch (e) {
      final errorMsg = '集成RFID服务失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 清空扫描池
  /// 在任一通道开始出馆流程时调用
  void clearPool() {
    try {
      debugPrint('清空共享扫描池，当前大小: ${_scanPool.length}');
      _scanPool.clear();

      // 🔥 同时清空现有RFID服务的扫描结果
      _rfidService?.clearScanResult();

      _notifyPoolChange();
      debugPrint('共享扫描池已清空');
    } catch (e) {
      final errorMsg = '清空扫描池失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 添加条码到扫描池
  /// 主机RFID扫描时调用，Set自动去重
  void addBarcode(String barcode) {
    if (barcode.isEmpty) return;
    
    try {
      final sizeBefore = _scanPool.length;
      _scanPool.add(barcode);
      
      // 只有真正添加了新条码才通知变化
      if (_scanPool.length > sizeBefore) {
        debugPrint('添加条码到共享池: $barcode (总计: ${_scanPool.length})');
        _notifyPoolChange();
      }
    } catch (e) {
      final errorMsg = '添加条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 批量添加条码到扫描池
  /// 用于从机同步数据
  void addBarcodes(List<String> barcodes) {
    if (barcodes.isEmpty) return;
    
    try {
      final sizeBefore = _scanPool.length;
      _scanPool.addAll(barcodes.where((b) => b.isNotEmpty));
      
      if (_scanPool.length > sizeBefore) {
        debugPrint('批量添加${barcodes.length}个条码，实际新增${_scanPool.length - sizeBefore}个');
        _notifyPoolChange();
      }
    } catch (e) {
      final errorMsg = '批量添加条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 同步扫描池数据（用于从机）
  /// 完全替换当前扫描池
  void syncPool(Set<String> newPool) {
    try {
      final oldSize = _scanPool.length;
      _scanPool.clear();
      _scanPool.addAll(newPool);
      
      debugPrint('同步扫描池: $oldSize -> ${_scanPool.length}');
      _notifyPoolChange();
    } catch (e) {
      final errorMsg = '同步扫描池失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 获取扫描池的JSON表示（用于网络传输）
  String toJson() {
    try {
      return jsonEncode(_scanPool.toList());
    } catch (e) {
      debugPrint('序列化扫描池失败: $e');
      return '[]';
    }
  }

  /// 从JSON恢复扫描池（用于网络传输）
  void fromJson(String jsonStr) {
    try {
      final List<dynamic> list = jsonDecode(jsonStr);
      final Set<String> newPool = list.cast<String>().toSet();
      syncPool(newPool);
    } catch (e) {
      final errorMsg = '反序列化扫描池失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
    }
  }

  /// 检查条码是否在扫描池中
  bool containsBarcode(String barcode) {
    return _scanPool.contains(barcode);
  }

  /// 移除指定条码（一般不需要，但提供接口）
  bool removeBarcode(String barcode) {
    try {
      final removed = _scanPool.remove(barcode);
      if (removed) {
        debugPrint('从扫描池移除条码: $barcode');
        _notifyPoolChange();
      }
      return removed;
    } catch (e) {
      final errorMsg = '移除条码失败: $e';
      debugPrint(errorMsg);
      _errorController.add(errorMsg);
      return false;
    }
  }

  /// 通知扫描池变化
  void _notifyPoolChange() {
    if (!_poolController.isClosed) {
      _poolController.add(Set<String>.from(_scanPool));
    }
  }

  /// 获取扫描池统计信息
  Map<String, dynamic> getStatistics() {
    return {
      'pool_size': _scanPool.length,
      'pool_content': _scanPool.toList(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// 清理资源
  void dispose() {
    _rfidBarcodeSubscription?.cancel();
    _rfidBarcodeSubscription = null;
    _rfidService = null;
    _scanPool.clear();
    _poolController.close();
    _errorController.close();
  }
}
